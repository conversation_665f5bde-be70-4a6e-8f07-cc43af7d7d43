#!/usr/bin/env python3
"""
Script de benchmark pentru măsurarea performanței aplicației optimizate
Compară consumul de resurse înainte și după optimizări
"""

import time
import psutil
import requests
import statistics
from concurrent.futures import ThreadPoolExecutor
import json

class PerformanceBenchmark:
    def __init__(self, base_url="http://localhost:5001"):
        self.base_url = base_url
        self.results = {}
        
    def measure_system_impact(self, duration=60):
        """Măsoară impactul aplicației asupra sistemului"""
        print(f"📊 Măsurare impact sistem pentru {duration} secunde...")
        
        cpu_samples = []
        memory_samples = []
        start_time = time.time()
        
        while time.time() - start_time < duration:
            cpu_samples.append(psutil.cpu_percent(interval=1))
            memory_samples.append(psutil.virtual_memory().percent)
            
        return {
            'cpu_avg': statistics.mean(cpu_samples),
            'cpu_max': max(cpu_samples),
            'memory_avg': statistics.mean(memory_samples),
            'memory_max': max(memory_samples),
            'duration': duration
        }
    
    def measure_api_performance(self, endpoint="/metrics", requests_count=100):
        """Măsoară performanța API-ului"""
        print(f"🚀 Test performanță API {endpoint} cu {requests_count} cereri...")
        
        response_times = []
        errors = 0
        
        def make_request():
            try:
                start = time.time()
                response = requests.get(f"{self.base_url}{endpoint}", timeout=5)
                end = time.time()
                
                if response.status_code == 200:
                    return end - start
                else:
                    return None
            except Exception:
                return None
        
        # Test secvențial
        start_time = time.time()
        for _ in range(requests_count):
            response_time = make_request()
            if response_time:
                response_times.append(response_time)
            else:
                errors += 1
        total_time = time.time() - start_time
        
        if response_times:
            return {
                'avg_response_time': statistics.mean(response_times),
                'min_response_time': min(response_times),
                'max_response_time': max(response_times),
                'median_response_time': statistics.median(response_times),
                'total_time': total_time,
                'requests_per_second': requests_count / total_time,
                'success_rate': (requests_count - errors) / requests_count * 100,
                'errors': errors
            }
        else:
            return {'error': 'Toate cererile au eșuat'}
    
    def measure_concurrent_load(self, endpoint="/metrics", concurrent_users=10, requests_per_user=10):
        """Test de încărcare concurentă"""
        print(f"⚡ Test încărcare concurentă: {concurrent_users} utilizatori, {requests_per_user} cereri/utilizator")
        
        def user_requests():
            response_times = []
            errors = 0
            
            for _ in range(requests_per_user):
                try:
                    start = time.time()
                    response = requests.get(f"{self.base_url}{endpoint}", timeout=5)
                    end = time.time()
                    
                    if response.status_code == 200:
                        response_times.append(end - start)
                    else:
                        errors += 1
                except Exception:
                    errors += 1
            
            return response_times, errors
        
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=concurrent_users) as executor:
            futures = [executor.submit(user_requests) for _ in range(concurrent_users)]
            results = [future.result() for future in futures]
        total_time = time.time() - start_time
        
        all_response_times = []
        total_errors = 0
        
        for response_times, errors in results:
            all_response_times.extend(response_times)
            total_errors += errors
        
        total_requests = concurrent_users * requests_per_user
        
        if all_response_times:
            return {
                'avg_response_time': statistics.mean(all_response_times),
                'min_response_time': min(all_response_times),
                'max_response_time': max(all_response_times),
                'median_response_time': statistics.median(all_response_times),
                'total_time': total_time,
                'requests_per_second': total_requests / total_time,
                'success_rate': (total_requests - total_errors) / total_requests * 100,
                'total_errors': total_errors,
                'concurrent_users': concurrent_users
            }
        else:
            return {'error': 'Toate cererile au eșuat'}
    
    def measure_memory_usage(self):
        """Măsoară utilizarea memoriei aplicației"""
        print("💾 Măsurare utilizare memorie...")
        
        # Găsește procesul Python care rulează aplicația
        app_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if 'python' in proc.info['name'].lower() and 'app.py' in ' '.join(proc.info['cmdline']):
                    app_processes.append(proc)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if app_processes:
            proc = app_processes[0]
            memory_info = proc.memory_info()
            return {
                'rss_mb': memory_info.rss / 1024 / 1024,  # Resident Set Size
                'vms_mb': memory_info.vms / 1024 / 1024,  # Virtual Memory Size
                'cpu_percent': proc.cpu_percent(interval=1),
                'num_threads': proc.num_threads(),
                'num_fds': proc.num_fds() if hasattr(proc, 'num_fds') else 'N/A'
            }
        else:
            return {'error': 'Nu s-a găsit procesul aplicației'}
    
    def run_full_benchmark(self):
        """Rulează benchmark complet"""
        print("🔥 Începe benchmark-ul complet al aplicației optimizate\n")
        
        # Test conectivitate
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            if response.status_code != 200:
                print("❌ Aplicația nu răspunde la /health")
                return
        except Exception as e:
            print(f"❌ Nu se poate conecta la aplicație: {e}")
            return
        
        print("✅ Aplicația este accesibilă\n")
        
        # Măsurători
        self.results['memory_usage'] = self.measure_memory_usage()
        self.results['api_performance'] = self.measure_api_performance()
        self.results['concurrent_load'] = self.measure_concurrent_load()
        self.results['system_impact'] = self.measure_system_impact(30)  # 30 secunde
        
        # Afișează rezultatele
        self.print_results()
        
        # Salvează rezultatele
        with open('benchmark_results.json', 'w') as f:
            json.dump(self.results, f, indent=2)
        print("\n📄 Rezultatele au fost salvate în benchmark_results.json")
    
    def print_results(self):
        """Afișează rezultatele benchmark-ului"""
        print("\n" + "="*60)
        print("📊 REZULTATE BENCHMARK")
        print("="*60)
        
        # Utilizare memorie
        if 'memory_usage' in self.results and 'error' not in self.results['memory_usage']:
            mem = self.results['memory_usage']
            print(f"\n💾 UTILIZARE MEMORIE:")
            print(f"   RSS: {mem['rss_mb']:.1f} MB")
            print(f"   VMS: {mem['vms_mb']:.1f} MB")
            print(f"   CPU: {mem['cpu_percent']:.1f}%")
            print(f"   Threads: {mem['num_threads']}")
        
        # Performanță API
        if 'api_performance' in self.results and 'error' not in self.results['api_performance']:
            api = self.results['api_performance']
            print(f"\n🚀 PERFORMANȚĂ API:")
            print(f"   Timp răspuns mediu: {api['avg_response_time']*1000:.1f} ms")
            print(f"   Timp răspuns median: {api['median_response_time']*1000:.1f} ms")
            print(f"   Cereri/secundă: {api['requests_per_second']:.1f}")
            print(f"   Rata de succes: {api['success_rate']:.1f}%")
        
        # Test concurent
        if 'concurrent_load' in self.results and 'error' not in self.results['concurrent_load']:
            conc = self.results['concurrent_load']
            print(f"\n⚡ TEST CONCURENT:")
            print(f"   Utilizatori concurenți: {conc['concurrent_users']}")
            print(f"   Timp răspuns mediu: {conc['avg_response_time']*1000:.1f} ms")
            print(f"   Cereri/secundă: {conc['requests_per_second']:.1f}")
            print(f"   Rata de succes: {conc['success_rate']:.1f}%")
        
        # Impact sistem
        if 'system_impact' in self.results:
            sys = self.results['system_impact']
            print(f"\n🖥️ IMPACT SISTEM:")
            print(f"   CPU mediu: {sys['cpu_avg']:.1f}%")
            print(f"   CPU maxim: {sys['cpu_max']:.1f}%")
            print(f"   Memorie medie: {sys['memory_avg']:.1f}%")
            print(f"   Memorie maximă: {sys['memory_max']:.1f}%")

if __name__ == "__main__":
    benchmark = PerformanceBenchmark()
    benchmark.run_full_benchmark()
