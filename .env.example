# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# Application Configuration - Optimized for Performance
METRICS_HISTORY_SIZE=20
UPDATE_INTERVAL=10
CACHE_TIMEOUT=2
MAX_PROCESSES=3

# Performance Features (true/false)
ENABLE_TEMPERATURE=false
ENABLE_DETAILED_PROCESSES=true

# Alert Thresholds (percentage)
CPU_ALERT_THRESHOLD=80
MEMORY_ALERT_THRESHOLD=85
DISK_ALERT_THRESHOLD=90

# Flask Configuration
FLASK_ENV=production
FLASK_DEBUG=False
FLASK_HOST=0.0.0.0
FLASK_PORT=5001
