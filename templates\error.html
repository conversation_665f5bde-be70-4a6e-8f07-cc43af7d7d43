<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Eroare - Dashboard Monitorizare</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --danger-color: #ef4444;
            --background-color: #f8fafc;
            --card-background: #ffffff;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-color: #e2e8f0;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background-color: var(--background-color);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .error-container {
            background: var(--card-background);
            border-radius: 1rem;
            padding: 3rem;
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow);
            text-align: center;
            max-width: 500px;
            width: 90%;
        }

        .error-icon {
            font-size: 4rem;
            color: var(--danger-color);
            margin-bottom: 1.5rem;
        }

        .error-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .error-message {
            font-size: 1.1rem;
            color: var(--text-secondary);
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .error-details {
            background: var(--background-color);
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 2rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            color: var(--danger-color);
            text-align: left;
            word-break: break-all;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: var(--primary-color);
            color: white;
            text-decoration: none;
            border-radius: 0.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: var(--card-background);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            margin-left: 1rem;
        }

        .btn-secondary:hover {
            background: var(--background-color);
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        
        <h1 class="error-title">Oops! Ceva nu a mers bine</h1>
        
        <p class="error-message">
            Ne pare rău, dar a apărut o eroare în timpul încărcării dashboard-ului de monitorizare.
        </p>
        
        {% if error %}
        <div class="error-details">
            <strong>Detalii eroare:</strong><br>
            {{ error }}
        </div>
        {% endif %}
        
        <div>
            <a href="/" class="btn">
                <i class="fas fa-home"></i>
                Întoarce-te la Dashboard
            </a>
            <a href="javascript:location.reload()" class="btn btn-secondary">
                <i class="fas fa-redo"></i>
                Reîncarcă Pagina
            </a>
        </div>
    </div>
</body>
</html>
