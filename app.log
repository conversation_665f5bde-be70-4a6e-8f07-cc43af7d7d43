2025-06-11 10:03:55,971 - werkzeug - WARNING -  * Debugger is active!
2025-06-11 10:03:55,985 - werkzeug - INFO -  * Debugger PIN: 757-************-06-11 10:04:18,071 - werkzeug - INFO -  * Detected change in 'c:\\Users\\<USER>\\Desktop\\productivitytoolkit\\app.py', reloading
2025-06-11 10:04:23,163 - werkzeug - WARNING -  * Debugger is active!
2025-06-11 10:04:23,174 - werkzeug - INFO -  * Debugger PIN: 757-************-06-11 10:04:26,537 - werkzeug - INFO - *********** - - [11/Jun/2025 10:04:26] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:04:28,535 - werkzeug - INFO - *********** - - [11/Jun/2025 10:04:28] "GET / HTTP/1.1" 200 -
2025-06-11 10:04:29,823 - werkzeug - INFO - *********** - - [11/Jun/2025 10:04:29] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:04:34,880 - werkzeug - INFO - *********** - - [11/Jun/2025 10:04:34] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:04:39,788 - werkzeug - INFO - *********** - - [11/Jun/2025 10:04:39] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:04:44,847 - werkzeug - INFO - *********** - - [11/Jun/2025 10:04:44] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:04:45,285 - werkzeug - INFO -  * Detected change in 'c:\\Users\\<USER>\\Desktop\\productivitytoolkit\\app.py', reloading
2025-06-11 10:04:50,649 - __main__ - INFO - Starting System Monitoring Dashboard...
2025-06-11 10:04:50,700 - __main__ - INFO - Alert thresholds: CPU=80%, Memory=85%, Disk=90%
2025-06-11 10:04:50,941 - werkzeug - WARNING -  * Debugger is active!
2025-06-11 10:04:50,979 - werkzeug - INFO -  * Debugger PIN: 757-************-06-11 10:04:52,417 - werkzeug - INFO - *********** - - [11/Jun/2025 10:04:52] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:04:53,434 - werkzeug - INFO -  * Detected change in 'c:\\Users\\<USER>\\Desktop\\productivitytoolkit\\app.py', reloading
2025-06-11 10:04:58,863 - __main__ - INFO - Starting System Monitoring Dashboard...
2025-06-11 10:04:58,870 - __main__ - INFO - Alert thresholds: CPU=80%, Memory=85%, Disk=90%
2025-06-11 10:04:58,909 - werkzeug - WARNING -  * Debugger is active!
2025-06-11 10:04:58,920 - werkzeug - INFO -  * Debugger PIN: 757-************-06-11 10:05:00,260 - werkzeug - INFO - *********** - - [11/Jun/2025 10:05:00] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:05:01,420 - werkzeug - INFO - *********** - - [11/Jun/2025 10:05:01] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:05:05,049 - werkzeug - INFO - *********** - - [11/Jun/2025 10:05:05] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:05:09,790 - werkzeug - INFO - *********** - - [11/Jun/2025 10:05:09] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:05:14,804 - werkzeug - INFO - *********** - - [11/Jun/2025 10:05:14] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:05:19,791 - werkzeug - INFO - *********** - - [11/Jun/2025 10:05:19] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:05:24,789 - werkzeug - INFO - *********** - - [11/Jun/2025 10:05:24] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:05:29,827 - werkzeug - INFO - *********** - - [11/Jun/2025 10:05:29] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:05:34,781 - werkzeug - INFO - *********** - - [11/Jun/2025 10:05:34] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:05:39,788 - werkzeug - INFO - *********** - - [11/Jun/2025 10:05:39] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:05:44,792 - werkzeug - INFO - *********** - - [11/Jun/2025 10:05:44] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:05:49,785 - werkzeug - INFO - *********** - - [11/Jun/2025 10:05:49] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:05:54,768 - werkzeug - INFO - *********** - - [11/Jun/2025 10:05:54] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:05:59,769 - werkzeug - INFO - *********** - - [11/Jun/2025 10:05:59] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:06:04,774 - werkzeug - INFO - *********** - - [11/Jun/2025 10:06:04] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:06:09,789 - werkzeug - INFO - *********** - - [11/Jun/2025 10:06:09] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:06:14,792 - werkzeug - INFO - *********** - - [11/Jun/2025 10:06:14] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:06:19,825 - werkzeug - INFO - *********** - - [11/Jun/2025 10:06:19] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:06:24,803 - werkzeug - INFO - *********** - - [11/Jun/2025 10:06:24] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:06:29,791 - werkzeug - INFO - *********** - - [11/Jun/2025 10:06:29] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:06:34,798 - werkzeug - INFO - *********** - - [11/Jun/2025 10:06:34] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:06:39,793 - werkzeug - INFO - *********** - - [11/Jun/2025 10:06:39] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:06:44,940 - werkzeug - INFO - *********** - - [11/Jun/2025 10:06:44] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:06:49,919 - werkzeug - INFO - *********** - - [11/Jun/2025 10:06:49] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:06:54,812 - werkzeug - INFO - *********** - - [11/Jun/2025 10:06:54] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:06:59,802 - werkzeug - INFO - *********** - - [11/Jun/2025 10:06:59] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:07:04,792 - werkzeug - INFO - *********** - - [11/Jun/2025 10:07:04] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:07:09,827 - werkzeug - INFO - *********** - - [11/Jun/2025 10:07:09] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:07:14,788 - werkzeug - INFO - *********** - - [11/Jun/2025 10:07:14] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:07:19,797 - werkzeug - INFO - *********** - - [11/Jun/2025 10:07:19] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:07:24,786 - werkzeug - INFO - *********** - - [11/Jun/2025 10:07:24] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:07:29,800 - werkzeug - INFO - *********** - - [11/Jun/2025 10:07:29] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:07:34,786 - werkzeug - INFO - *********** - - [11/Jun/2025 10:07:34] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:07:39,792 - werkzeug - INFO - *********** - - [11/Jun/2025 10:07:39] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:07:44,790 - werkzeug - INFO - *********** - - [11/Jun/2025 10:07:44] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:07:49,785 - werkzeug - INFO - *********** - - [11/Jun/2025 10:07:49] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:07:54,789 - werkzeug - INFO - *********** - - [11/Jun/2025 10:07:54] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:07:59,787 - werkzeug - INFO - *********** - - [11/Jun/2025 10:07:59] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:08:04,789 - werkzeug - INFO - *********** - - [11/Jun/2025 10:08:04] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:08:09,791 - werkzeug - INFO - *********** - - [11/Jun/2025 10:08:09] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:08:14,789 - werkzeug - INFO - *********** - - [11/Jun/2025 10:08:14] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:08:19,811 - werkzeug - INFO - *********** - - [11/Jun/2025 10:08:19] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:08:24,796 - werkzeug - INFO - *********** - - [11/Jun/2025 10:08:24] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:08:29,789 - werkzeug - INFO - *********** - - [11/Jun/2025 10:08:29] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:08:34,795 - werkzeug - INFO - *********** - - [11/Jun/2025 10:08:34] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:08:39,800 - werkzeug - INFO - *********** - - [11/Jun/2025 10:08:39] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:08:44,940 - werkzeug - INFO - *********** - - [11/Jun/2025 10:08:44] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:08:49,791 - werkzeug - INFO - *********** - - [11/Jun/2025 10:08:49] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:08:54,793 - werkzeug - INFO - *********** - - [11/Jun/2025 10:08:54] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:08:59,798 - werkzeug - INFO - *********** - - [11/Jun/2025 10:08:59] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:09:04,799 - werkzeug - INFO - *********** - - [11/Jun/2025 10:09:04] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:09:09,791 - werkzeug - INFO - *********** - - [11/Jun/2025 10:09:09] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:09:14,802 - werkzeug - INFO - *********** - - [11/Jun/2025 10:09:14] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:09:19,858 - werkzeug - INFO - *********** - - [11/Jun/2025 10:09:19] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:09:24,793 - werkzeug - INFO - *********** - - [11/Jun/2025 10:09:24] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:09:29,804 - werkzeug - INFO - *********** - - [11/Jun/2025 10:09:29] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:09:34,805 - werkzeug - INFO - *********** - - [11/Jun/2025 10:09:34] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:09:39,882 - werkzeug - INFO - *********** - - [11/Jun/2025 10:09:39] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:09:44,988 - werkzeug - INFO - *********** - - [11/Jun/2025 10:09:44] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:09:49,906 - werkzeug - INFO - *********** - - [11/Jun/2025 10:09:49] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:09:54,795 - werkzeug - INFO - *********** - - [11/Jun/2025 10:09:54] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:09:59,808 - werkzeug - INFO - *********** - - [11/Jun/2025 10:09:59] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:10:04,924 - werkzeug - INFO - *********** - - [11/Jun/2025 10:10:04] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:10:09,792 - werkzeug - INFO - *********** - - [11/Jun/2025 10:10:09] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:10:14,827 - werkzeug - INFO - *********** - - [11/Jun/2025 10:10:14] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:10:19,803 - werkzeug - INFO - *********** - - [11/Jun/2025 10:10:19] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:10:24,829 - werkzeug - INFO - *********** - - [11/Jun/2025 10:10:24] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:10:29,838 - werkzeug - INFO - *********** - - [11/Jun/2025 10:10:29] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:10:34,949 - werkzeug - INFO - *********** - - [11/Jun/2025 10:10:34] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:10:39,807 - werkzeug - INFO - *********** - - [11/Jun/2025 10:10:39] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:10:44,915 - werkzeug - INFO - *********** - - [11/Jun/2025 10:10:44] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:10:49,796 - werkzeug - INFO - *********** - - [11/Jun/2025 10:10:49] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:10:56,363 - werkzeug - INFO - *********** - - [11/Jun/2025 10:10:56] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:11:00,830 - werkzeug - INFO - *********** - - [11/Jun/2025 10:11:00] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:11:04,882 - werkzeug - INFO - *********** - - [11/Jun/2025 10:11:04] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:11:09,810 - werkzeug - INFO - *********** - - [11/Jun/2025 10:11:09] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:11:14,805 - werkzeug - INFO - *********** - - [11/Jun/2025 10:11:14] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:11:19,825 - werkzeug - INFO - *********** - - [11/Jun/2025 10:11:19] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:11:24,948 - werkzeug - INFO - *********** - - [11/Jun/2025 10:11:24] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:11:29,981 - werkzeug - INFO - *********** - - [11/Jun/2025 10:11:29] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:11:34,786 - werkzeug - INFO - *********** - - [11/Jun/2025 10:11:34] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:11:39,820 - werkzeug - INFO - *********** - - [11/Jun/2025 10:11:39] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:11:50,090 - __main__ - INFO - Starting System Monitoring Dashboard...
2025-06-11 10:11:50,098 - __main__ - INFO - Alert thresholds: CPU=80%, Memory=85%, Disk=90%
2025-06-11 10:11:50,167 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://***********:5001
2025-06-11 10:11:50,167 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-11 10:11:50,169 - werkzeug - INFO -  * Restarting with stat
2025-06-11 10:11:54,907 - __main__ - INFO - Starting System Monitoring Dashboard...
2025-06-11 10:11:55,042 - __main__ - INFO - Alert thresholds: CPU=80%, Memory=85%, Disk=90%
2025-06-11 10:11:55,092 - werkzeug - WARNING -  * Debugger is active!
2025-06-11 10:11:55,103 - werkzeug - INFO -  * Debugger PIN: 757-************-06-11 10:11:56,410 - werkzeug - INFO - *********** - - [11/Jun/2025 10:11:56] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:11:57,584 - werkzeug - INFO - *********** - - [11/Jun/2025 10:11:57] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:11:59,800 - werkzeug - INFO - *********** - - [11/Jun/2025 10:11:59] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:12:02,656 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\productivitytoolkit\\app.py', reloading
2025-06-11 10:12:02,872 - werkzeug - INFO -  * Restarting with stat
2025-06-11 10:12:08,074 - __main__ - INFO - Starting System Monitoring Dashboard...
2025-06-11 10:12:08,174 - __main__ - INFO - Alert thresholds: CPU=80%, Memory=85%, Disk=90%
2025-06-11 10:12:08,228 - werkzeug - WARNING -  * Debugger is active!
2025-06-11 10:12:08,236 - werkzeug - INFO -  * Debugger PIN: 757-************-06-11 10:12:09,944 - werkzeug - INFO - *********** - - [11/Jun/2025 10:12:09] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:12:11,126 - werkzeug - INFO - *********** - - [11/Jun/2025 10:12:11] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:12:13,496 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\productivitytoolkit\\app.py', reloading
2025-06-11 10:12:14,074 - werkzeug - INFO -  * Restarting with stat
2025-06-11 10:12:19,877 - __main__ - WARNING - Redis connection failed: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
2025-06-11 10:12:19,885 - __main__ - INFO - Starting System Monitoring Dashboard...
2025-06-11 10:12:19,892 - __main__ - INFO - Alert thresholds: CPU=80%, Memory=85%, Disk=90%
2025-06-11 10:12:19,934 - werkzeug - WARNING -  * Debugger is active!
2025-06-11 10:12:19,945 - werkzeug - INFO -  * Debugger PIN: 757-************-06-11 10:12:21,308 - werkzeug - INFO - *********** - - [11/Jun/2025 10:12:21] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:12:22,477 - werkzeug - INFO - *********** - - [11/Jun/2025 10:12:22] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:12:25,482 - werkzeug - INFO - *********** - - [11/Jun/2025 10:12:25] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:12:25,490 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\productivitytoolkit\\app.py', reloading
2025-06-11 10:12:25,762 - werkzeug - INFO -  * Restarting with stat
2025-06-11 10:12:31,171 - __main__ - WARNING - Redis connection failed: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
2025-06-11 10:12:31,180 - __main__ - INFO - Starting System Monitoring Dashboard...
2025-06-11 10:12:31,189 - __main__ - INFO - Alert thresholds: CPU=80%, Memory=85%, Disk=90%
2025-06-11 10:12:31,226 - werkzeug - WARNING -  * Debugger is active!
2025-06-11 10:12:31,239 - werkzeug - INFO -  * Debugger PIN: 757-************-06-11 10:12:32,559 - werkzeug - INFO - *********** - - [11/Jun/2025 10:12:32] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:12:34,842 - werkzeug - INFO - *********** - - [11/Jun/2025 10:12:34] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:12:37,649 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\productivitytoolkit\\app.py', reloading
2025-06-11 10:12:37,926 - werkzeug - INFO -  * Restarting with stat
2025-06-11 10:12:43,165 - __main__ - WARNING - Redis connection failed: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
2025-06-11 10:12:43,173 - __main__ - INFO - Starting System Monitoring Dashboard...
2025-06-11 10:12:43,173 - __main__ - INFO - Redis connection: Disconnected
2025-06-11 10:12:43,173 - __main__ - INFO - Alert thresholds: CPU=80%, Memory=85%, Disk=90%
2025-06-11 10:12:43,208 - werkzeug - WARNING -  * Debugger is active!
2025-06-11 10:12:43,220 - werkzeug - INFO -  * Debugger PIN: 757-************-06-11 10:12:44,662 - werkzeug - INFO - *********** - - [11/Jun/2025 10:12:44] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:12:45,865 - werkzeug - INFO - *********** - - [11/Jun/2025 10:12:45] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:12:49,769 - werkzeug - INFO - *********** - - [11/Jun/2025 10:12:49] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:12:54,799 - werkzeug - INFO - *********** - - [11/Jun/2025 10:12:54] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:12:59,802 - werkzeug - INFO - *********** - - [11/Jun/2025 10:12:59] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:13:04,997 - werkzeug - INFO - *********** - - [11/Jun/2025 10:13:04] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:13:09,952 - werkzeug - INFO - *********** - - [11/Jun/2025 10:13:09] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:13:14,788 - werkzeug - INFO - *********** - - [11/Jun/2025 10:13:14] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:13:15,409 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 10:13:15] "GET /health HTTP/1.1" 200 -
2025-06-11 10:13:19,799 - werkzeug - INFO - *********** - - [11/Jun/2025 10:13:19] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:13:22,138 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 10:13:22] "GET / HTTP/1.1" 200 -
2025-06-11 10:13:23,311 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 10:13:23] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-11 10:13:24,700 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 10:13:24] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:13:26,296 - werkzeug - INFO - *********** - - [11/Jun/2025 10:13:26] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:13:29,595 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 10:13:29] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:13:30,572 - werkzeug - INFO - *********** - - [11/Jun/2025 10:13:30] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:13:34,486 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 10:13:34] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:13:35,645 - werkzeug - INFO - *********** - - [11/Jun/2025 10:13:35] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:13:39,620 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 10:13:39] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:13:41,083 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 10:13:41] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:13:41,179 - werkzeug - INFO - *********** - - [11/Jun/2025 10:13:41] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:13:46,756 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 10:13:46] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:13:47,701 - werkzeug - INFO - *********** - - [11/Jun/2025 10:13:47] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:13:49,960 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 10:13:49] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:13:51,094 - werkzeug - INFO - *********** - - [11/Jun/2025 10:13:51] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:13:55,040 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 10:13:55] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:13:55,616 - werkzeug - INFO - *********** - - [11/Jun/2025 10:13:55] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:14:01,033 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 10:14:01] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:14:01,045 - werkzeug - INFO - *********** - - [11/Jun/2025 10:14:01] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:14:05,459 - werkzeug - INFO - *********** - - [11/Jun/2025 10:14:05] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:14:10,614 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 10:14:10] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:14:10,708 - werkzeug - INFO - *********** - - [11/Jun/2025 10:14:10] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:14:15,701 - werkzeug - INFO - *********** - - [11/Jun/2025 10:14:15] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:14:16,321 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 10:14:16] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:14:19,520 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 10:14:19] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:14:20,561 - werkzeug - INFO - *********** - - [11/Jun/2025 10:14:20] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:14:26,845 - werkzeug - INFO - *********** - - [11/Jun/2025 10:14:26] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:14:28,215 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 10:14:28] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:14:29,847 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 10:14:29] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:14:36,646 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 10:14:36] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:14:42,562 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 10:14:42] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:14:55,836 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 10:14:55] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:15:01,642 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 10:15:01] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:15:05,235 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 10:15:05] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:17:29,657 - __main__ - WARNING - Redis connection failed: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
2025-06-11 10:17:29,663 - __main__ - INFO - Starting System Monitoring Dashboard...
2025-06-11 10:17:29,663 - __main__ - INFO - Redis connection: Disconnected
2025-06-11 10:17:29,663 - __main__ - INFO - Alert thresholds: CPU=80%, Memory=85%, Disk=90%
2025-06-11 10:17:29,723 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://***********:5001
2025-06-11 10:17:29,723 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-11 10:17:29,725 - werkzeug - INFO -  * Restarting with stat
2025-06-11 10:17:34,330 - __main__ - WARNING - Redis connection failed: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
2025-06-11 10:17:34,339 - __main__ - INFO - Starting System Monitoring Dashboard...
2025-06-11 10:17:34,340 - __main__ - INFO - Redis connection: Disconnected
2025-06-11 10:17:34,340 - __main__ - INFO - Alert thresholds: CPU=80%, Memory=85%, Disk=90%
2025-06-11 10:17:34,390 - werkzeug - WARNING -  * Debugger is active!
2025-06-11 10:17:34,402 - werkzeug - INFO -  * Debugger PIN: 757-************-06-11 10:17:35,725 - werkzeug - INFO - *********** - - [11/Jun/2025 10:17:35] "GET / HTTP/1.1" 200 -
2025-06-11 10:17:37,695 - werkzeug - INFO - *********** - - [11/Jun/2025 10:17:37] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:17:42,878 - werkzeug - INFO - *********** - - [11/Jun/2025 10:17:42] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:17:47,650 - werkzeug - INFO - *********** - - [11/Jun/2025 10:17:47] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:17:54,870 - werkzeug - INFO - *********** - - [11/Jun/2025 10:17:54] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:18:09,289 - werkzeug - INFO - *********** - - [11/Jun/2025 10:18:09] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:18:14,702 - werkzeug - INFO - *********** - - [11/Jun/2025 10:18:14] "GET /metrics HTTP/1.1" 200 -
2025-06-11 10:21:16,367 - __main__ - WARNING - Redis connection failed: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
2025-06-11 10:21:16,384 - __main__ - INFO - Starting System Monitoring Dashboard...
2025-06-11 10:21:16,385 - __main__ - INFO - Redis connection: Disconnected
2025-06-11 10:21:16,386 - __main__ - INFO - Alert thresholds: CPU=80%, Memory=85%, Disk=90%
2025-06-11 10:21:16,614 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://***********:5001
2025-06-11 10:21:16,624 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-11 10:21:16,641 - werkzeug - INFO -  * Restarting with stat
2025-06-11 10:21:21,348 - __main__ - WARNING - Redis connection failed: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
2025-06-11 10:21:21,356 - __main__ - INFO - Starting System Monitoring Dashboard...
2025-06-11 10:21:21,356 - __main__ - INFO - Redis connection: Disconnected
2025-06-11 10:21:21,356 - __main__ - INFO - Alert thresholds: CPU=80%, Memory=85%, Disk=90%
2025-06-11 10:21:21,400 - werkzeug - WARNING -  * Debugger is active!
2025-06-11 10:21:21,425 - werkzeug - INFO -  * Debugger PIN: 757-************-06-11 10:21:31,493 - werkzeug - INFO - 127.0.0.1 - - [11/Jun/2025 10:21:31] "GET /health HTTP/1.1" 200 -
