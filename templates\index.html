<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Monitorizare Sistem</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --background-color: #f8fafc;
            --card-background: #ffffff;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-color: #e2e8f0;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        [data-theme="dark"] {
            --background-color: #0f172a;
            --card-background: #1e293b;
            --text-primary: #f1f5f9;
            --text-secondary: #94a3b8;
            --border-color: #334155;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background-color: var(--background-color);
            color: var(--text-primary);
            line-height: 1.6;
            transition: all 0.3s ease;
        }

        .header {
            background: var(--card-background);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            box-shadow: var(--shadow);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .controls {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .theme-toggle {
            background: none;
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 0.5rem;
            border-radius: 0.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .theme-toggle:hover {
            background: var(--background-color);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem 1rem;
        }

        .status-bar {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: var(--card-background);
            border-radius: 0.5rem;
            border: 1px solid var(--border-color);
            font-size: 0.875rem;
        }

        .status-online { color: var(--success-color); }
        .status-offline { color: var(--danger-color); }

        .alerts-container {
            margin-bottom: 2rem;
        }

        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            animation: slideIn 0.3s ease;
        }

        .alert-warning {
            background: rgba(245, 158, 11, 0.1);
            border: 1px solid var(--warning-color);
            color: var(--warning-color);
        }

        .alert-critical {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid var(--danger-color);
            color: var(--danger-color);
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .metric-card {
            background: var(--card-background);
            border-radius: 1rem;
            padding: 1.5rem;
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.2);
        }

        .metric-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .metric-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .metric-icon {
            width: 2rem;
            height: 2rem;
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .metric-subtitle {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-bottom: 1rem;
        }

        .progress-bar {
            width: 100%;
            height: 0.5rem;
            background: var(--background-color);
            border-radius: 0.25rem;
            overflow: hidden;
            margin-bottom: 0.5rem;
        }

        .progress-fill {
            height: 100%;
            border-radius: 0.25rem;
            transition: all 0.3s ease;
        }

        .progress-success { background: var(--success-color); }
        .progress-warning { background: var(--warning-color); }
        .progress-danger { background: var(--danger-color); }

        .cpu-icon { background: rgba(37, 99, 235, 0.1); color: var(--primary-color); }
        .memory-icon { background: rgba(16, 185, 129, 0.1); color: var(--success-color); }
        .disk-icon { background: rgba(245, 158, 11, 0.1); color: var(--warning-color); }
        .network-icon { background: rgba(139, 92, 246, 0.1); color: #8b5cf6; }
        .temp-icon { background: rgba(239, 68, 68, 0.1); color: var(--danger-color); }

        .charts-section {
            margin-top: 2rem;
        }

        .chart-container {
            background: var(--card-background);
            border-radius: 1rem;
            padding: 1.5rem;
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow);
            margin-bottom: 1.5rem;
        }

        .chart-wrapper {
            position: relative;
            height: 400px; /* Înălțime fixă pentru grafic */
            width: 100%;
        }

        .chart-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .processes-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .processes-table th,
        .processes-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .processes-table th {
            background: var(--background-color);
            font-weight: 600;
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .last-updated {
            text-align: center;
            margin-top: 2rem;
            padding: 1rem;
            background: var(--card-background);
            border-radius: 0.5rem;
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
        }

        .export-buttons {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: 1px solid var(--border-color);
            background: var(--card-background);
            color: var(--text-primary);
            border-radius: 0.5rem;
            cursor: pointer;
            text-decoration: none;
            font-size: 0.875rem;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: var(--background-color);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background: #1d4ed8;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            .metrics-grid {
                grid-template-columns: 1fr;
            }

            .status-bar {
                justify-content: center;
            }

            .chart-wrapper {
                height: 300px; /* Înălțime mai mică pe mobile */
            }

            .export-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-chart-line"></i>
                Dashboard Monitorizare Sistem
            </div>
            <div class="controls">
                <div class="export-buttons">
                    <a href="/api/export/json" class="btn" download>
                        <i class="fas fa-download"></i> JSON
                    </a>
                    <a href="/api/export/csv" class="btn" download>
                        <i class="fas fa-file-csv"></i> CSV
                    </a>
                </div>
                <button class="theme-toggle" onclick="toggleTheme()">
                    <i class="fas fa-moon" id="theme-icon"></i>
                </button>
            </div>
        </div>
    </header>

    <div class="container">
        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-item">
                <i class="fas fa-circle status-online" id="redis-status"></i>
                <span>Redis: <span id="redis-text">Conectat</span></span>
            </div>
            <div class="status-item">
                <i class="fas fa-clock"></i>
                <span>Actualizare: {{ config.UPDATE_INTERVAL }}s</span>
            </div>
            <div class="status-item">
                <i class="fas fa-database"></i>
                <span>Istoric: {{ config.METRICS_HISTORY_SIZE }} înregistrări</span>
            </div>
        </div>

        <!-- Alerts -->
        <div class="alerts-container" id="alerts-container">
            <!-- Alerts will be populated by JavaScript -->
        </div>

        <!-- Metrics Grid -->
        <div class="metrics-grid">
            <!-- CPU Card -->
            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-title">Utilizare CPU</span>
                    <div class="metric-icon cpu-icon">
                        <i class="fas fa-microchip"></i>
                    </div>
                </div>
                <div class="metric-value" id="cpu-value">
                    {{ current_metrics.cpu if current_metrics else 0 }}%
                </div>
                <div class="metric-subtitle">Procesare sistem</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="cpu-progress" style="width: {{ current_metrics.cpu if current_metrics else 0 }}%"></div>
                </div>
            </div>

            <!-- Memory Card -->
            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-title">Utilizare Memorie</span>
                    <div class="metric-icon memory-icon">
                        <i class="fas fa-memory"></i>
                    </div>
                </div>
                <div class="metric-value" id="memory-value">
                    {{ current_metrics.memory if current_metrics else 0 }}%
                </div>
                <div class="metric-subtitle" id="memory-details">
                    {{ current_metrics.memory_used_gb if current_metrics else 0 }}GB / {{ current_metrics.memory_total_gb if current_metrics else 0 }}GB
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="memory-progress" style="width: {{ current_metrics.memory if current_metrics else 0 }}%"></div>
                </div>
            </div>

            <!-- Disk Card -->
            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-title">Utilizare Disc</span>
                    <div class="metric-icon disk-icon">
                        <i class="fas fa-hdd"></i>
                    </div>
                </div>
                <div class="metric-value" id="disk-value">
                    {{ current_metrics.disk if current_metrics else 0 }}%
                </div>
                <div class="metric-subtitle" id="disk-details">
                    {{ current_metrics.disk_used_gb if current_metrics else 0 }}GB / {{ current_metrics.disk_total_gb if current_metrics else 0 }}GB
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="disk-progress" style="width: {{ current_metrics.disk if current_metrics else 0 }}%"></div>
                </div>
            </div>

            <!-- Network Card -->
            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-title">Trafic Rețea</span>
                    <div class="metric-icon network-icon">
                        <i class="fas fa-network-wired"></i>
                    </div>
                </div>
                <div class="metric-value" id="network-value">
                    <span style="font-size: 1.2rem;">↑ <span id="network-sent">0</span>MB</span><br>
                    <span style="font-size: 1.2rem;">↓ <span id="network-recv">0</span>MB</span>
                </div>
                <div class="metric-subtitle">Trimis / Primit</div>
            </div>

            <!-- Temperature Card (if available) -->
            <div class="metric-card" id="temp-card" style="display: none;">
                <div class="metric-header">
                    <span class="metric-title">Temperatură</span>
                    <div class="metric-icon temp-icon">
                        <i class="fas fa-thermometer-half"></i>
                    </div>
                </div>
                <div class="metric-value" id="temp-value">--°C</div>
                <div class="metric-subtitle">Temperatură sistem</div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="charts-section">
            <div class="chart-container">
                <h3 class="chart-title">Istoric Utilizare Resurse</h3>
                <div class="chart-wrapper">
                    <canvas id="metricsChart"></canvas>
                </div>
            </div>

            <!-- Top Processes -->
            <div class="chart-container">
                <h3 class="chart-title">Procese Active (Top 5)</h3>
                <table class="processes-table" id="processes-table">
                    <thead>
                        <tr>
                            <th>PID</th>
                            <th>Nume</th>
                            <th>CPU %</th>
                            <th>Memorie %</th>
                        </tr>
                    </thead>
                    <tbody id="processes-body">
                        <!-- Processes will be populated by JavaScript -->
                    </tbody>
                </table>
            </div>
        </div>

        <div class="last-updated">
            Ultima actualizare: <span id="last-update">{{ current_metrics.time if current_metrics else 'Niciodată' }}</span>
        </div>
    </div>

    <script>
        // Global variables - optimized
        let metricsChart;
        let isDarkTheme = localStorage.getItem('darkTheme') === 'true';
        let isPageVisible = true;
        let updateInterval;
        let lastUpdateTime = 0;

        // Initialize theme
        if (isDarkTheme) {
            document.documentElement.setAttribute('data-theme', 'dark');
            document.getElementById('theme-icon').className = 'fas fa-sun';
        }

        // Theme toggle function
        function toggleTheme() {
            isDarkTheme = !isDarkTheme;
            localStorage.setItem('darkTheme', isDarkTheme);

            if (isDarkTheme) {
                document.documentElement.setAttribute('data-theme', 'dark');
                document.getElementById('theme-icon').className = 'fas fa-sun';
            } else {
                document.documentElement.removeAttribute('data-theme');
                document.getElementById('theme-icon').className = 'fas fa-moon';
            }
        }

        // Initialize Chart.js
        function initChart() {
            const ctx = document.getElementById('metricsChart').getContext('2d');
            metricsChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'CPU %',
                        data: [],
                        borderColor: '#2563eb',
                        backgroundColor: 'rgba(37, 99, 235, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'Memorie %',
                        data: [],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'Disc %',
                        data: [],
                        borderColor: '#f59e0b',
                        backgroundColor: 'rgba(245, 158, 11, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 20
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: 'white',
                            bodyColor: 'white',
                            borderColor: 'rgba(255, 255, 255, 0.1)',
                            borderWidth: 1
                        }
                    },
                    elements: {
                        point: {
                            radius: 4,
                            hoverRadius: 6
                        },
                        line: {
                            borderWidth: 2
                        }
                    }
                }
            });
        }

        // Update progress bar color based on value
        function getProgressColor(value, type = 'default') {
            if (type === 'cpu' && value > 80) return 'progress-danger';
            if (type === 'memory' && value > 85) return 'progress-danger';
            if (type === 'disk' && value > 90) return 'progress-danger';
            if (value > 70) return 'progress-warning';
            return 'progress-success';
        }

        // Update metrics display
        function updateMetrics(data) {
            // Update CPU
            document.getElementById('cpu-value').textContent = data.cpu + '%';
            const cpuProgress = document.getElementById('cpu-progress');
            cpuProgress.style.width = data.cpu + '%';
            cpuProgress.className = 'progress-fill ' + getProgressColor(data.cpu, 'cpu');

            // Update Memory
            document.getElementById('memory-value').textContent = data.memory + '%';
            document.getElementById('memory-details').textContent =
                `${data.memory_used_gb}GB / ${data.memory_total_gb}GB`;
            const memoryProgress = document.getElementById('memory-progress');
            memoryProgress.style.width = data.memory + '%';
            memoryProgress.className = 'progress-fill ' + getProgressColor(data.memory, 'memory');

            // Update Disk
            document.getElementById('disk-value').textContent = data.disk + '%';
            document.getElementById('disk-details').textContent =
                `${data.disk_used_gb}GB / ${data.disk_total_gb}GB`;
            const diskProgress = document.getElementById('disk-progress');
            diskProgress.style.width = data.disk + '%';
            diskProgress.className = 'progress-fill ' + getProgressColor(data.disk, 'disk');

            // Update Network
            if (data.network) {
                document.getElementById('network-sent').textContent =
                    Math.round(data.network.bytes_sent / (1024 * 1024));
                document.getElementById('network-recv').textContent =
                    Math.round(data.network.bytes_recv / (1024 * 1024));
            }

            // Update Temperature (if available)
            if (data.temperature !== null && data.temperature !== undefined) {
                document.getElementById('temp-card').style.display = 'block';
                document.getElementById('temp-value').textContent = data.temperature + '°C';
            }

            // Update timestamp
            document.getElementById('last-update').textContent = data.time;

            // Update chart
            updateChart(data);

            // Update processes table
            updateProcessesTable(data.top_processes || []);

            // Update alerts
            updateAlerts(data.alerts || []);
        }

        // Optimized chart update with throttling
        function updateChart(data) {
            if (!metricsChart || !isPageVisible) return;

            const labels = metricsChart.data.labels;
            const cpuData = metricsChart.data.datasets[0].data;
            const memoryData = metricsChart.data.datasets[1].data;
            const diskData = metricsChart.data.datasets[2].data;

            // Add new data point
            const timeLabel = new Date(data.timestamp).toLocaleTimeString('ro-RO', {
                hour: '2-digit',
                minute: '2-digit'
            });
            labels.push(timeLabel);
            cpuData.push(data.cpu);
            memoryData.push(data.memory);
            diskData.push(data.disk);

            // Keep only last 15 data points (reduced from 20)
            if (labels.length > 15) {
                labels.shift();
                cpuData.shift();
                memoryData.shift();
                diskData.shift();
            }

            // Use 'none' animation for better performance
            metricsChart.update('none');
        }

        // Update processes table
        function updateProcessesTable(processes) {
            const tbody = document.getElementById('processes-body');
            tbody.innerHTML = '';

            processes.forEach(proc => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td>${proc.pid}</td>
                    <td>${proc.name}</td>
                    <td>${proc.cpu_percent.toFixed(1)}%</td>
                    <td>${proc.memory_percent.toFixed(1)}%</td>
                `;
            });
        }

        // Update alerts
        function updateAlerts(alerts) {
            const container = document.getElementById('alerts-container');
            container.innerHTML = '';

            alerts.forEach(alert => {
                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${alert.severity}`;
                alertDiv.innerHTML = `
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>${alert.message}</span>
                `;
                container.appendChild(alertDiv);
            });
        }

        // Optimized fetch with page visibility and throttling
        async function fetchMetrics() {
            // Skip if page is not visible or too frequent updates
            const now = Date.now();
            if (!isPageVisible || (now - lastUpdateTime < 2000)) {
                return;
            }
            lastUpdateTime = now;

            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 5000); // 5s timeout

                const response = await fetch('/metrics', {
                    signal: controller.signal,
                    cache: 'no-cache'
                });
                clearTimeout(timeoutId);

                if (!response.ok) throw new Error('Network response was not ok');

                const data = await response.json();
                updateMetrics(data);

                // Update Redis status
                document.getElementById('redis-status').className = 'fas fa-circle status-online';
                document.getElementById('redis-text').textContent = 'Conectat';

            } catch (error) {
                if (error.name !== 'AbortError') {
                    console.error('Error fetching metrics:', error);
                }
                document.getElementById('redis-status').className = 'fas fa-circle status-offline';
                document.getElementById('redis-text').textContent = 'Deconectat';
            }
        }

        // Page visibility optimization
        document.addEventListener('visibilitychange', function() {
            isPageVisible = !document.hidden;
            if (isPageVisible) {
                fetchMetrics(); // Immediate update when page becomes visible
            }
        });

        // Initialize everything when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initChart();
            fetchMetrics();

            // Set up optimized periodic updates
            const intervalTime = {{ config.UPDATE_INTERVAL * 1000 if config else 10000 }};
            updateInterval = setInterval(fetchMetrics, intervalTime);

            // Cleanup on page unload
            window.addEventListener('beforeunload', function() {
                if (updateInterval) {
                    clearInterval(updateInterval);
                }
            });
        });
    </script>
</body>
</html>
