# 🖥️ Dashboard Monitorizare Sistem

Un dashboard modern și interactiv pentru monitorizarea resurselor sistemului în timp real, construit cu Flask și tehnologii web moderne.

## ✨ Caracteristici

### 📊 Monitorizare Completă
- **CPU**: Utilizare în timp real cu istoric
- **Memorie**: Utilizare și detalii despre RAM
- **Disc**: Spațiu utilizat și disponibil
- **Rețea**: Trafic de date în/out
- **Temperatură**: Monitorizare termică (dacă este disponibilă)
- **Procese**: Top 5 procese active

### 🎨 Interfață Modernă
- Design responsive și modern
- Tema dark/light cu comutare
- Grafice interactive cu Chart.js
- Animații fluide și notificări
- Compatibilitate mobilă

### 🚨 Sistem de Alerte
- Praguri configurabile pentru CPU, memorie și disc
- Alerte vizuale în timp real
- Notificări pentru utilizare critică

### 📈 Funcționalități Avansate
- Istoric metrics cu Redis
- Export date în JSON/CSV
- API RESTful pentru integrări
- Rate limiting pentru securitate
- Logging complet

## 🚀 Instalare și Configurare

### Cerințe
- Python 3.8+
- Redis Server (opțional, pentru istoric)

### Instalare Rapidă

1. **Clonează repository-ul**
```bash
git clone <repository-url>
cd productivitytoolkit
```

2. **Instalează dependențele**
```bash
pip install -r requirements.txt
```

3. **Configurează variabilele de mediu** (opțional)
```bash
cp .env.example .env
# Editează .env cu setările tale
```

4. **Pornește aplicația**
```bash
python app.py
```

5. **Accesează dashboard-ul**
Deschide browserul la: `http://localhost:5001`

## ⚙️ Configurare

### Variabile de Mediu

| Variabilă | Valoare Implicită | Descriere |
|-----------|-------------------|-----------|
| `REDIS_HOST` | localhost | Host-ul Redis |
| `REDIS_PORT` | 6379 | Portul Redis |
| `REDIS_DB` | 0 | Baza de date Redis |
| `METRICS_HISTORY_SIZE` | 50 | Numărul de metrics salvate |
| `UPDATE_INTERVAL` | 5 | Interval actualizare (secunde) |
| `CPU_ALERT_THRESHOLD` | 80 | Prag alertă CPU (%) |
| `MEMORY_ALERT_THRESHOLD` | 85 | Prag alertă memorie (%) |
| `DISK_ALERT_THRESHOLD` | 90 | Prag alertă disc (%) |

### Redis (Opțional)
Pentru a salva istoricul metrics, instalează și pornește Redis:

**Windows:**
```bash
# Descarcă Redis de la https://redis.io/download
# Sau folosește Docker:
docker run -d -p 6379:6379 redis:alpine
```

**Linux/macOS:**
```bash
# Ubuntu/Debian
sudo apt-get install redis-server

# macOS
brew install redis
redis-server
```

## 📡 API Endpoints

### Metrics
- `GET /metrics` - Metrics curente
- `GET /api/metrics/history?limit=20` - Istoric metrics
- `GET /api/alerts` - Alerte active

### Export
- `GET /api/export/json` - Export JSON
- `GET /api/export/csv` - Export CSV

### Utilități
- `GET /health` - Health check

## 🛠️ Dezvoltare

### Structura Proiectului
```
productivitytoolkit/
├── app.py                 # Aplicația principală Flask
├── requirements.txt       # Dependențe Python
├── .env.example          # Exemplu configurare
├── README.md             # Documentație
├── templates/
│   ├── index.html        # Dashboard principal
│   └── error.html        # Pagina de eroare
└── logs/
    └── app.log           # Log-uri aplicație
```

### Funcționalități Tehnice
- **Rate Limiting**: Protecție împotriva spam-ului
- **Error Handling**: Gestionare robustă a erorilor
- **Logging**: Înregistrare completă a activității
- **Responsive Design**: Compatibilitate mobilă
- **Real-time Updates**: Actualizări automate

## 🎯 Utilizare

1. **Dashboard Principal**: Vizualizează toate metrics-urile în timp real
2. **Grafice Interactive**: Urmărește tendințele în timp
3. **Alerte**: Primește notificări pentru utilizare ridicată
4. **Export Date**: Descarcă istoricul în JSON sau CSV
5. **Tema Dark/Light**: Comută între teme pentru confort vizual

## ⚡ Optimizări de Performanță

### Configurație Optimizată

Pentru sisteme cu resurse limitate, folosește configurația optimizată:

```bash
# Copiază configurația optimizată
cp .env.optimized .env

# Restart aplicația
python app.py
```

### Optimizări Implementate

**Backend:**
- ✅ Caching metrics (2-5 secunde)
- ✅ Interval actualizare mărit (10-15s)
- ✅ Procese limitate (2-3 în loc de 5)
- ✅ Logging redus (doar erori)
- ✅ Rate limiting optimizat
- ✅ Operații psutil optimizate

**Frontend:**
- ✅ Actualizări doar când pagina e vizibilă
- ✅ Throttling cereri (max 1 la 2 secunde)
- ✅ Grafice cu animații reduse
- ✅ Cleanup automat la închidere

**Configurații Disponibile:**

| Mod | UPDATE_INTERVAL | CACHE_TIMEOUT | MAX_PROCESSES | Consum CPU | Consum RAM |
|-----|----------------|---------------|---------------|------------|------------|
| Normal | 5s | 2s | 5 | ~2-5% | ~15-25MB |
| Optimizat | 10s | 5s | 3 | ~1-3% | ~10-15MB |
| Ultra-light | 30s | 10s | 1 | ~0.5-1% | ~5-10MB |

### Benchmark Performanță

Rulează benchmark pentru a măsura impactul:

```bash
pip install requests
python benchmark.py
```

## 🔧 Troubleshooting

### Probleme Comune

**Redis nu se conectează:**
- Verifică dacă Redis rulează: `redis-cli ping`
- Aplicația va funcționa fără Redis, dar fără istoric

**Metrics nu se actualizează:**
- Verifică console-ul browser pentru erori JavaScript
- Asigură-te că portul 5001 nu este blocat

**Performanță lentă:**
- Folosește configurația optimizată: `cp .env.optimized .env`
- Dezactivează funcționalități opționale: `ENABLE_TEMPERATURE=false`
- Mărește intervalul de actualizare: `UPDATE_INTERVAL=15`

## 📝 Licență

Acest proiect este open source și disponibil sub licența MIT.

## 🤝 Contribuții

Contribuțiile sunt binevenite! Te rugăm să:
1. Faci fork la repository
2. Creezi o branch pentru feature-ul tău
3. Faci commit cu modificările
4. Deschizi un Pull Request

## 📞 Suport

Pentru probleme sau întrebări, deschide un issue în repository sau contactează echipa de dezvoltare.

---

**Dezvoltat cu ❤️ pentru monitorizarea eficientă a sistemului**
