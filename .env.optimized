# Configurație optimizată pentru performanță minimă
# Copiază acest fișier în .env pentru a activa optimizările

# Redis Configuration (opțional - poate fi dezactivat)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# Configurație optimizată pentru performanță
METRICS_HISTORY_SIZE=10
UPDATE_INTERVAL=15
CACHE_TIMEOUT=5
MAX_PROCESSES=2

# Funcționalități opționale - dezactivate pentru performanță
ENABLE_TEMPERATURE=false
ENABLE_DETAILED_PROCESSES=false
ENABLE_NETWORK_DETAILS=false

# Praguri de alertă - mai permisive
CPU_ALERT_THRESHOLD=90
MEMORY_ALERT_THRESHOLD=90
DISK_ALERT_THRESHOLD=95

# Flask - configurație de producție
FLASK_ENV=production
FLASK_DEBUG=false
FLASK_HOST=127.0.0.1
FLASK_PORT=5001

# Rate limiting restrictiv
RATE_LIMIT_METRICS=30
RATE_LIMIT_HISTORY=10
RATE_LIMIT_EXPORT=2

# Logging minim
LOG_LEVEL=ERROR
LOG_TO_FILE=false

# Mod ultra-ușor pentru sisteme foarte limitate
# Decomentează linia de mai jos pentru consum minim de resurse
# ULTRA_LIGHT_MODE=true
