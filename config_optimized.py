"""
Configurație optimizată pentru performanță minimă
Acest fișier conține setări pentru a reduce la minimum impactul asupra sistemului
"""

import os

class OptimizedConfig:
    """Configurație optimizată pentru consum minim de resurse"""
    
    # Redis - opțional pentru istoric
    REDIS_HOST = os.getenv("REDIS_HOST", "localhost")
    REDIS_PORT = int(os.getenv("REDIS_PORT", 6379))
    REDIS_DB = int(os.getenv("REDIS_DB", 0))
    
    # Setări de performanță - valori conservative
    METRICS_HISTORY_SIZE = int(os.getenv("METRICS_HISTORY_SIZE", 10))  # Minim istoric
    UPDATE_INTERVAL = int(os.getenv("UPDATE_INTERVAL", 15))  # 15 secunde între actualizări
    CACHE_TIMEOUT = int(os.getenv("CACHE_TIMEOUT", 5))  # <PERSON><PERSON> mai lung
    MAX_PROCESSES = int(os.getenv("MAX_PROCESSES", 2))  # Doar top 2 procese
    
    # Funcționalități opționale - dezactivate pentru performanță
    ENABLE_TEMPERATURE = os.getenv("ENABLE_TEMPERATURE", "false").lower() == "true"
    ENABLE_DETAILED_PROCESSES = os.getenv("ENABLE_DETAILED_PROCESSES", "false").lower() == "true"
    ENABLE_NETWORK_DETAILS = os.getenv("ENABLE_NETWORK_DETAILS", "false").lower() == "true"
    
    # Praguri de alertă - mai permisive pentru a reduce verificările
    ALERT_THRESHOLDS = {
        'cpu': int(os.getenv("CPU_ALERT_THRESHOLD", 90)),  # Prag mai mare
        'memory': int(os.getenv("MEMORY_ALERT_THRESHOLD", 90)),
        'disk': int(os.getenv("DISK_ALERT_THRESHOLD", 95))
    }
    
    # Setări Flask pentru producție
    FLASK_ENV = os.getenv("FLASK_ENV", "production")
    FLASK_DEBUG = os.getenv("FLASK_DEBUG", "false").lower() == "true"
    FLASK_HOST = os.getenv("FLASK_HOST", "127.0.0.1")  # Doar localhost
    FLASK_PORT = int(os.getenv("FLASK_PORT", 5001))
    
    # Rate limiting mai restrictiv
    RATE_LIMIT_METRICS = int(os.getenv("RATE_LIMIT_METRICS", 30))  # 30 req/min
    RATE_LIMIT_HISTORY = int(os.getenv("RATE_LIMIT_HISTORY", 10))  # 10 req/min
    RATE_LIMIT_EXPORT = int(os.getenv("RATE_LIMIT_EXPORT", 2))    # 2 req/5min
    
    # Logging minim
    LOG_LEVEL = os.getenv("LOG_LEVEL", "ERROR")
    LOG_TO_FILE = os.getenv("LOG_TO_FILE", "false").lower() == "true"

class UltraLightConfig:
    """Configurație ultra-ușoară pentru sisteme cu resurse foarte limitate"""
    
    # Fără Redis
    REDIS_HOST = None
    REDIS_PORT = None
    REDIS_DB = None
    
    # Setări minime
    METRICS_HISTORY_SIZE = 5  # Doar 5 înregistrări
    UPDATE_INTERVAL = 30  # 30 secunde
    CACHE_TIMEOUT = 10  # Cache lung
    MAX_PROCESSES = 1  # Doar un proces
    
    # Toate funcționalitățile opționale dezactivate
    ENABLE_TEMPERATURE = False
    ENABLE_DETAILED_PROCESSES = False
    ENABLE_NETWORK_DETAILS = False
    
    # Fără alerte
    ALERT_THRESHOLDS = {
        'cpu': 100,  # Niciodată alertă
        'memory': 100,
        'disk': 100
    }
    
    # Flask minim
    FLASK_ENV = "production"
    FLASK_DEBUG = False
    FLASK_HOST = "127.0.0.1"
    FLASK_PORT = 5001
    
    # Rate limiting foarte restrictiv
    RATE_LIMIT_METRICS = 10  # 10 req/min
    RATE_LIMIT_HISTORY = 2   # 2 req/min
    RATE_LIMIT_EXPORT = 1    # 1 req/10min
    
    # Logging dezactivat
    LOG_LEVEL = "CRITICAL"
    LOG_TO_FILE = False

# Instrucțiuni de utilizare
USAGE_INSTRUCTIONS = """
Pentru a utiliza configurația optimizată:

1. Copiază .env.optimized în .env:
   cp .env.optimized .env

2. Pentru configurația ultra-ușoară, setează în .env:
   ULTRA_LIGHT_MODE=true

3. Restart aplicația:
   python app.py

Configurația optimizată reduce:
- Frecvența actualizărilor (15s în loc de 5s)
- Numărul de procese monitorizate (2 în loc de 5)
- Dimensiunea cache-ului (10 în loc de 50)
- Funcționalitățile opționale (temperatură, detalii procese)
- Nivelul de logging (doar erori)

Pentru sisteme foarte limitate, folosește ULTRA_LIGHT_MODE=true
"""
