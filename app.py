from flask import Flask, render_template, jsonify, request
import psutil
import datetime
import redis
import os
import json
import logging
from functools import wraps, lru_cache
import time
from collections import defaultdict
import threading
import gc
from weakref import WeakValueDictionary

# Configure optimized logging
from logging.handlers import RotatingFileHandler

logging.basicConfig(
    level=logging.WARNING,  # Reduced logging level
    format='%(asctime)s - %(levelname)s - %(message)s',  # Simplified format
    handlers=[
        RotatingFileHandler('app.log', maxBytes=1024*1024, backupCount=1),  # Log rotation
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Optimized Configuration
class Config:
    REDIS_HOST = os.getenv("REDIS_HOST", "localhost")
    REDIS_PORT = int(os.getenv("REDIS_PORT", 6379))
    REDIS_DB = int(os.getenv("REDIS_DB", 0))
    METRICS_HISTORY_SIZE = int(os.getenv("METRICS_HISTORY_SIZE", 20))  # Reduced from 50
    UPDATE_INTERVAL = int(os.getenv("UPDATE_INTERVAL", 10))  # Increased from 5 seconds
    CACHE_TIMEOUT = int(os.getenv("CACHE_TIMEOUT", 2))  # Cache metrics for 2 seconds
    MAX_PROCESSES = int(os.getenv("MAX_PROCESSES", 3))  # Reduced from 5
    ENABLE_TEMPERATURE = os.getenv("ENABLE_TEMPERATURE", "false").lower() == "true"
    ENABLE_DETAILED_PROCESSES = os.getenv("ENABLE_DETAILED_PROCESSES", "true").lower() == "true"
    ALERT_THRESHOLDS = {
        'cpu': int(os.getenv("CPU_ALERT_THRESHOLD", 80)),
        'memory': int(os.getenv("MEMORY_ALERT_THRESHOLD", 85)),
        'disk': int(os.getenv("DISK_ALERT_THRESHOLD", 90))
    }

# Global cache for metrics
_metrics_cache = {'data': None, 'timestamp': 0}
_cache_lock = threading.Lock()

# Optimized rate limiting storage with automatic cleanup
request_counts = defaultdict(list)
_last_cleanup = 0

def rate_limit(max_requests=60, window=60):
    """Optimized rate limiting decorator"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            global _last_cleanup
            client_ip = request.remote_addr
            now = time.time()

            # Periodic cleanup (every 60 seconds)
            if now - _last_cleanup > 60:
                for ip in list(request_counts.keys()):
                    request_counts[ip] = [
                        req_time for req_time in request_counts[ip]
                        if now - req_time < window
                    ]
                    if not request_counts[ip]:
                        del request_counts[ip]
                _last_cleanup = now

            # Quick check for current IP
            request_counts[client_ip] = [
                req_time for req_time in request_counts[client_ip]
                if now - req_time < window
            ]

            # Check rate limit
            if len(request_counts[client_ip]) >= max_requests:
                return jsonify({'error': 'Rate limit exceeded'}), 429

            # Add current request
            request_counts[client_ip].append(now)
            return f(*args, **kwargs)
        return decorated_function
    return decorator

# Connect to Redis with improved error handling
def init_redis():
    try:
        redis_client = redis.Redis(
            host=Config.REDIS_HOST,
            port=Config.REDIS_PORT,
            db=Config.REDIS_DB,
            decode_responses=True,
            socket_timeout=5,
            socket_connect_timeout=5,
            retry_on_timeout=True
        )
        redis_client.ping()
        logger.info("Redis connection established successfully")
        return redis_client
    except redis.exceptions.ConnectionError as e:
        logger.warning(f"Redis connection failed: {e}")
        return None
    except Exception as e:
        logger.error(f"Unexpected Redis error: {e}")
        return None

redis_client = init_redis()

def get_system_metrics():
    """Get optimized system metrics with caching"""
    current_time = time.time()

    # Check cache first
    with _cache_lock:
        if (_metrics_cache['data'] is not None and
            current_time - _metrics_cache['timestamp'] < Config.CACHE_TIMEOUT):
            return _metrics_cache['data']

    try:
        # Basic metrics - optimized calls
        cpu_percent = psutil.cpu_percent(interval=0.1)  # Reduced interval
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')

        # Network metrics - only if needed
        net_io = psutil.net_io_counters()

        # Process metrics - optimized and limited
        processes = []
        if Config.ENABLE_DETAILED_PROCESSES:
            try:
                # Use oneshot() for better performance
                for proc in list(psutil.process_iter(['pid', 'name']))[:50]:  # Limit to first 50 processes
                    try:
                        with proc.oneshot():
                            cpu_pct = proc.cpu_percent()
                            mem_pct = proc.memory_percent()
                            if cpu_pct > 1.0 or mem_pct > 2.0:  # Higher thresholds
                                processes.append({
                                    'pid': proc.pid,
                                    'name': proc.name(),
                                    'cpu_percent': round(cpu_pct, 1),
                                    'memory_percent': round(mem_pct, 1)
                                })
                    except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                        continue

                    # Limit to top processes only
                    if len(processes) >= Config.MAX_PROCESSES * 2:
                        break

                # Sort and limit
                processes = sorted(processes, key=lambda x: x['cpu_percent'], reverse=True)[:Config.MAX_PROCESSES]
            except Exception:
                processes = []  # Fail silently for processes

        # Temperature - only if enabled
        temperature = None
        if Config.ENABLE_TEMPERATURE:
            try:
                temps = psutil.sensors_temperatures()
                if temps:
                    for name, entries in temps.items():
                        if entries:
                            temperature = round(entries[0].current, 1)
                            break
            except (AttributeError, OSError):
                pass

        # Build metrics object
        now = datetime.datetime.now()
        metrics = {
            'timestamp': now.isoformat(),
            'time': now.strftime('%Y-%m-%d %H:%M:%S'),
            'cpu': round(cpu_percent, 1),
            'memory': round(memory.percent, 1),
            'memory_used_gb': round(memory.used / (1024**3), 1),
            'memory_total_gb': round(memory.total / (1024**3), 1),
            'disk': round(disk.percent, 1),
            'disk_used_gb': round(disk.used / (1024**3), 1),
            'disk_total_gb': round(disk.total / (1024**3), 1),
            'network': {
                'bytes_sent': net_io.bytes_sent,
                'bytes_recv': net_io.bytes_recv,
                'packets_sent': net_io.packets_sent,
                'packets_recv': net_io.packets_recv
            },
            'top_processes': processes,
            'temperature': temperature,
            'alerts': check_alerts(cpu_percent, memory.percent, disk.percent)
        }

        # Store in Redis asynchronously (non-blocking)
        if redis_client:
            try:
                # Use pipeline for better performance
                pipe = redis_client.pipeline()
                pipe.lpush("metrics", json.dumps(metrics))
                pipe.ltrim("metrics", 0, Config.METRICS_HISTORY_SIZE - 1)
                pipe.execute()
            except Exception:
                pass  # Fail silently for Redis operations

        # Update cache
        with _cache_lock:
            _metrics_cache['data'] = metrics
            _metrics_cache['timestamp'] = current_time

        return metrics

    except Exception as e:
        logger.error(f"Error getting system metrics: {e}")
        return {
            'error': 'Failed to retrieve system metrics',
            'time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

def check_alerts(cpu, memory, disk):
    """Check if any metrics exceed alert thresholds"""
    alerts = []

    if cpu > Config.ALERT_THRESHOLDS['cpu']:
        alerts.append({
            'type': 'cpu',
            'message': f'High CPU usage: {cpu}%',
            'severity': 'warning' if cpu < 90 else 'critical'
        })

    if memory > Config.ALERT_THRESHOLDS['memory']:
        alerts.append({
            'type': 'memory',
            'message': f'High memory usage: {memory}%',
            'severity': 'warning' if memory < 95 else 'critical'
        })

    if disk > Config.ALERT_THRESHOLDS['disk']:
        alerts.append({
            'type': 'disk',
            'message': f'High disk usage: {disk}%',
            'severity': 'warning' if disk < 95 else 'critical'
        })

    return alerts

def get_historical_metrics():
    """Get historical metrics from Redis"""
    if not redis_client:
        return []

    try:
        metrics_list = redis_client.lrange("metrics", 0, -1)
        return [json.loads(m) for m in metrics_list]
    except Exception as e:
        logger.error(f"Error retrieving historical metrics: {e}")
        return []

@app.route('/')
def index():
    """Main dashboard page"""
    try:
        current_metrics = get_system_metrics()
        historical_metrics = get_historical_metrics()

        return render_template('index.html',
                             current_metrics=current_metrics,
                             historical_metrics=historical_metrics,
                             config=Config)
    except Exception as e:
        logger.error(f"Error rendering index page: {e}")
        return render_template('error.html', error=str(e)), 500

@app.route('/metrics')
@rate_limit(max_requests=200, window=60)  # Increased for better performance
def metrics():
    """Optimized API endpoint for current metrics"""
    return jsonify(get_system_metrics())

@app.route('/api/metrics/history')
@rate_limit(max_requests=20, window=60)  # Reduced frequency for heavy operation
def metrics_history():
    """Optimized API endpoint for historical metrics"""
    try:
        limit = min(request.args.get('limit', 10, type=int), 20)  # Reduced default and max

        historical_metrics = get_historical_metrics()[:limit]
        return jsonify({
            'metrics': historical_metrics,
            'count': len(historical_metrics)
        })
    except Exception:
        return jsonify({'error': 'Failed to retrieve metrics history'}), 500

@app.route('/api/export/<format>')
@rate_limit(max_requests=5, window=300)  # Very limited for heavy operations
def export_metrics(format):
    """Optimized export metrics in different formats"""
    try:
        # Limit export to recent data only
        historical_metrics = get_historical_metrics()[:10]  # Only last 10 entries

        if format.lower() == 'json':
            return jsonify(historical_metrics)
        elif format.lower() == 'csv':
            import csv
            import io

            output = io.StringIO()
            if historical_metrics:
                fieldnames = ['timestamp', 'cpu', 'memory', 'disk']  # Reduced fields
                writer = csv.DictWriter(output, fieldnames=fieldnames)
                writer.writeheader()

                for metric in historical_metrics:
                    row = {field: metric.get(field, '') for field in fieldnames}
                    writer.writerow(row)

            response = app.response_class(
                output.getvalue(),
                mimetype='text/csv',
                headers={'Content-Disposition': 'attachment; filename=metrics.csv'}
            )
            return response
        else:
            return jsonify({'error': 'Unsupported format'}), 400

    except Exception:
        return jsonify({'error': 'Export failed'}), 500

@app.route('/api/alerts')
@rate_limit(max_requests=30, window=60)  # Reduced frequency
def get_alerts():
    """Get current alerts - optimized"""
    try:
        current_metrics = get_system_metrics()
        return jsonify({
            'alerts': current_metrics.get('alerts', []),
            'timestamp': current_metrics.get('timestamp')
        })
    except Exception:
        return jsonify({'error': 'Failed to retrieve alerts'}), 500

@app.route('/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'redis_connected': redis_client is not None,
        'timestamp': datetime.datetime.now().isoformat()
    })

@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Endpoint not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    logger.error(f"Internal server error: {error}")
    return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    logger.info("Starting System Monitoring Dashboard...")
    logger.info(f"Redis connection: {'Connected' if redis_client else 'Disconnected'}")
    logger.info(f"Alert thresholds: CPU={Config.ALERT_THRESHOLDS['cpu']}%, Memory={Config.ALERT_THRESHOLDS['memory']}%, Disk={Config.ALERT_THRESHOLDS['disk']}%")

    app.run(host='0.0.0.0', port=5001, debug=True)
