from flask import Flask, render_template, jsonify, request
import psutil
import datetime
import redis
import os

import json
import logging
from functools import wraps
import time
from collections import defaultdict

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Configuration
class Config:
    REDIS_HOST = os.getenv("REDIS_HOST", "localhost")
    REDIS_PORT = int(os.getenv("REDIS_PORT", 6379))
    REDIS_DB = int(os.getenv("REDIS_DB", 0))
    METRICS_HISTORY_SIZE = int(os.getenv("METRICS_HISTORY_SIZE", 50))
    UPDATE_INTERVAL = int(os.getenv("UPDATE_INTERVAL", 5))
    ALERT_THRESHOLDS = {
        'cpu': int(os.getenv("CPU_ALERT_THRESHOLD", 80)),
        'memory': int(os.getenv("MEMORY_ALERT_THRESHOLD", 85)),
        'disk': int(os.getenv("DISK_ALERT_THRESHOLD", 90))
    }

# Rate limiting storage
request_counts = defaultdict(list)

def rate_limit(max_requests=60, window=60):
    """Rate limiting decorator"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            client_ip = request.remote_addr
            now = time.time()

            # Clean old requests
            request_counts[client_ip] = [
                req_time for req_time in request_counts[client_ip]
                if now - req_time < window
            ]

            # Check rate limit
            if len(request_counts[client_ip]) >= max_requests:
                return jsonify({'error': 'Rate limit exceeded'}), 429

            # Add current request
            request_counts[client_ip].append(now)
            return f(*args, **kwargs)
        return decorated_function
    return decorator

# Connect to Redis with improved error handling
def init_redis():
    try:
        redis_client = redis.Redis(
            host=Config.REDIS_HOST,
            port=Config.REDIS_PORT,
            db=Config.REDIS_DB,
            decode_responses=True,
            socket_timeout=5,
            socket_connect_timeout=5,
            retry_on_timeout=True
        )
        redis_client.ping()
        logger.info("Redis connection established successfully")
        return redis_client
    except redis.exceptions.ConnectionError as e:
        logger.warning(f"Redis connection failed: {e}")
        return None
    except Exception as e:
        logger.error(f"Unexpected Redis error: {e}")
        return None

redis_client = init_redis()

def get_system_metrics():
    """Get comprehensive system metrics"""
    try:
        # Basic metrics
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')

        # Network metrics
        net_io = psutil.net_io_counters()

        # Process metrics
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
            try:
                proc_info = proc.info
                if proc_info['cpu_percent'] > 0 or proc_info['memory_percent'] > 1:
                    processes.append(proc_info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        # Sort by CPU usage and get top 5
        processes = sorted(processes, key=lambda x: x['cpu_percent'], reverse=True)[:5]

        # Temperature (if available)
        temperature = None
        try:
            temps = psutil.sensors_temperatures()
            if temps:
                # Get first available temperature sensor
                for name, entries in temps.items():
                    if entries:
                        temperature = entries[0].current
                        break
        except (AttributeError, OSError):
            pass

        metrics = {
            'timestamp': datetime.datetime.now().isoformat(),
            'time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'cpu': round(cpu_percent, 2),
            'memory': round(memory.percent, 2),
            'memory_used_gb': round(memory.used / (1024**3), 2),
            'memory_total_gb': round(memory.total / (1024**3), 2),
            'disk': round(disk.percent, 2),
            'disk_used_gb': round(disk.used / (1024**3), 2),
            'disk_total_gb': round(disk.total / (1024**3), 2),
            'network': {
                'bytes_sent': net_io.bytes_sent,
                'bytes_recv': net_io.bytes_recv,
                'packets_sent': net_io.packets_sent,
                'packets_recv': net_io.packets_recv
            },
            'top_processes': processes,
            'temperature': temperature,
            'alerts': check_alerts(cpu_percent, memory.percent, disk.percent)
        }

        # Store in Redis with improved error handling
        if redis_client:
            try:
                redis_client.lpush("metrics", json.dumps(metrics))
                redis_client.ltrim("metrics", 0, Config.METRICS_HISTORY_SIZE - 1)
                logger.debug("Metrics stored in Redis successfully")
            except Exception as e:
                logger.error(f"Failed to store metrics in Redis: {e}")

        return metrics

    except Exception as e:
        logger.error(f"Error getting system metrics: {e}")
        return {
            'error': 'Failed to retrieve system metrics',
            'time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

def check_alerts(cpu, memory, disk):
    """Check if any metrics exceed alert thresholds"""
    alerts = []

    if cpu > Config.ALERT_THRESHOLDS['cpu']:
        alerts.append({
            'type': 'cpu',
            'message': f'High CPU usage: {cpu}%',
            'severity': 'warning' if cpu < 90 else 'critical'
        })

    if memory > Config.ALERT_THRESHOLDS['memory']:
        alerts.append({
            'type': 'memory',
            'message': f'High memory usage: {memory}%',
            'severity': 'warning' if memory < 95 else 'critical'
        })

    if disk > Config.ALERT_THRESHOLDS['disk']:
        alerts.append({
            'type': 'disk',
            'message': f'High disk usage: {disk}%',
            'severity': 'warning' if disk < 95 else 'critical'
        })

    return alerts

def get_historical_metrics():
    """Get historical metrics from Redis"""
    if not redis_client:
        return []

    try:
        metrics_list = redis_client.lrange("metrics", 0, -1)
        return [json.loads(m) for m in metrics_list]
    except Exception as e:
        logger.error(f"Error retrieving historical metrics: {e}")
        return []

@app.route('/')
def index():
    """Main dashboard page"""
    try:
        current_metrics = get_system_metrics()
        historical_metrics = get_historical_metrics()

        return render_template('index.html',
                             current_metrics=current_metrics,
                             historical_metrics=historical_metrics,
                             config=Config)
    except Exception as e:
        logger.error(f"Error rendering index page: {e}")
        return render_template('error.html', error=str(e)), 500

@app.route('/metrics')
@rate_limit(max_requests=120, window=60)  # Allow more requests for real-time updates
def metrics():
    """API endpoint for current metrics"""
    return jsonify(get_system_metrics())

@app.route('/api/metrics/history')
@rate_limit(max_requests=30, window=60)
def metrics_history():
    """API endpoint for historical metrics"""
    try:
        limit = request.args.get('limit', 20, type=int)
        limit = min(limit, Config.METRICS_HISTORY_SIZE)  # Cap the limit

        historical_metrics = get_historical_metrics()[:limit]
        return jsonify({
            'metrics': historical_metrics,
            'count': len(historical_metrics)
        })
    except Exception as e:
        logger.error(f"Error retrieving metrics history: {e}")
        return jsonify({'error': 'Failed to retrieve metrics history'}), 500

@app.route('/api/export/<format>')
@rate_limit(max_requests=10, window=60)
def export_metrics(format):
    """Export metrics in different formats"""
    try:
        historical_metrics = get_historical_metrics()

        if format.lower() == 'json':
            return jsonify(historical_metrics)
        elif format.lower() == 'csv':
            import csv
            import io

            output = io.StringIO()
            if historical_metrics:
                fieldnames = ['timestamp', 'cpu', 'memory', 'disk', 'memory_used_gb', 'disk_used_gb']
                writer = csv.DictWriter(output, fieldnames=fieldnames)
                writer.writeheader()

                for metric in historical_metrics:
                    row = {field: metric.get(field, '') for field in fieldnames}
                    writer.writerow(row)

            response = app.response_class(
                output.getvalue(),
                mimetype='text/csv',
                headers={'Content-Disposition': 'attachment; filename=metrics.csv'}
            )
            return response
        else:
            return jsonify({'error': 'Unsupported format. Use json or csv'}), 400

    except Exception as e:
        logger.error(f"Error exporting metrics: {e}")
        return jsonify({'error': 'Failed to export metrics'}), 500

@app.route('/api/alerts')
@rate_limit(max_requests=60, window=60)
def get_alerts():
    """Get current alerts"""
    try:
        current_metrics = get_system_metrics()
        return jsonify({
            'alerts': current_metrics.get('alerts', []),
            'timestamp': current_metrics.get('timestamp')
        })
    except Exception as e:
        logger.error(f"Error retrieving alerts: {e}")
        return jsonify({'error': 'Failed to retrieve alerts'}), 500

@app.route('/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'redis_connected': redis_client is not None,
        'timestamp': datetime.datetime.now().isoformat()
    })

@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Endpoint not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    logger.error(f"Internal server error: {error}")
    return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    logger.info("Starting System Monitoring Dashboard...")
    logger.info(f"Redis connection: {'Connected' if redis_client else 'Disconnected'}")
    logger.info(f"Alert thresholds: CPU={Config.ALERT_THRESHOLDS['cpu']}%, Memory={Config.ALERT_THRESHOLDS['memory']}%, Disk={Config.ALERT_THRESHOLDS['disk']}%")

    app.run(host='0.0.0.0', port=5001, debug=True)
